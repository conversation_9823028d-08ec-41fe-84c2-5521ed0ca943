package lessonDataFunc

import (
	"deskcrm/api/achilles"
	"deskcrm/api/dal"
	"deskcrm/api/das"
	"deskcrm/api/dataproxy"
	"deskcrm/api/jxdascore"
	"deskcrm/api/jxexamui"
	"deskcrm/api/moat"
	"deskcrm/api/pcassistant"
	"deskcrm/api/writereport"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/models"
	"deskcrm/service/arkBase/dataQuery"
	"fmt"
	"slices"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func (s *Format) GetLessonName(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		lessonName := ""
		if _, ok := lessonMap[lessonID]; ok {
			lessonName = lessonMap[lessonID].LessonName
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonName)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节名称】 ", "dal courseinfo")
	return
}

// GetType 获取章节类型
// 对应PHP中的type字段，来源于lessonType
func (s *Format) GetType(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		lessonType := 0
		if _, ok := lessonMap[lessonID]; ok {
			lessonType = lessonMap[lessonID].LessonType
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lessonType)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节类型】", "dal courseinfo")
	return
}

// GetPlayType 获取播放类型
// 对应PHP中的playType字段
func (s *Format) GetPlayType(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		playType := 0
		if _, ok := lessonMap[lessonID]; ok {
			playType = lessonMap[lessonID].PlayType
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playType)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取播放类型】", "dal courseinfo")
	return
}

// GetInclassTime 获取上课时间
// 对应PHP中的inclassTime字段，格式化为"YYYY-MM-DD HH:MM-HH:MM"
func (s *Format) GetInclassTime(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		inclassTime := ""
		if _, ok := lessonMap[lessonID]; ok {
			startTime := lessonMap[lessonID].StartTime
			stopTime := lessonMap[lessonID].StopTime
			// 格式化为 "2006-01-02 15:04-15:04" 格式，对应PHP的LESSON_INCLASS_TIME
			startTimeStr := time.Unix(int64(startTime), 0).Format("2006-01-02 15:04")
			stopTimeStr := time.Unix(int64(stopTime), 0).Format("15:04")
			inclassTime = startTimeStr + "-" + stopTimeStr
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取上课时间】", "dal courseinfo")
	return
}

// GetStopTime 获取章节结束时间
// 对应PHP中的stopTime字段
func (s *Format) GetStopTime(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		stopTime := 0
		if _, ok := lessonMap[lessonID]; ok {
			stopTime = lessonMap[lessonID].StopTime
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, stopTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节结束时间】", "dal courseinfo")
	return
}

// GetStartTime 获取章节开始时间
// 对应PHP中的startTime字段
func (s *Format) GetStartTime(ctx *gin.Context) (err error) {
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := queryData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		startTime := 0
		if _, ok := lessonMap[lessonID]; ok {
			startTime = lessonMap[lessonID].StartTime
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, startTime)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取章节开始时间】", "dal courseinfo")
	return
}

// GetPreview 获取预习数据
// 对应PHP中的preview字段
func (s *Format) GetPreview(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) {
		return
	}

	// 获取基础LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取课程信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)
	gradeId := int(courseData.MainGradeId)
	subjectId := int(courseData.MainSubjectId)

	// 获取学段信息
	gradeStage := define.Grade2XB[gradeId]

	// 获取iLab信息（仅针对初二物理）
	ilabInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	ilabData := ilabInfo.(*dataQuery.ILabInfo)

	// 获取预习开启状态信息
	previewOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetPreviewOpenInfo", []interface{}{
		s.param.CourseID, s.param.LessonIDs, gradeStage,
	})
	if err != nil {
		return
	}
	previewOpenData := previewOpenInfo.(map[int64]dataQuery.PreviewOpenInfo)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化预习数据数组：[显示文本, 颜色, 是否可点击]
		previewArray := LessonDataArray(consts.HeaderDefaultValue, consts.ColorGray, 1)

		if lessonLuData, ok := luData[lessonID]; ok {
			correctNum := lessonLuData.PreviewCorrectNum
			participateNum := lessonLuData.PreviewParticipateNum
			totalNum := lessonLuData.PreviewTotalNum

			// 基础预习数据格式化
			if totalNum == 0 {
				previewArray[0] = consts.HeaderDefaultValue
			} else {
				previewArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, totalNum)
			}

			// iLab兼容逻辑 - 针对初二物理课程
			if gradeId == 3 && subjectId == 4 {
				if checkInfo, exists := ilabData.CheckIlabLesson[lessonID]; exists && checkInfo.ILabLesson > 0 {
					if level, hasLevel := ilabData.PreviewInfoByIlab[lessonID]; hasLevel {
						if levelText, ok := jxexamui.LevelIlabMap[level]; ok && levelText != "" {
							previewArray[0] = levelText
							// 根据iLab等级设置颜色
							switch level {
							case 1:
								previewArray[1] = consts.ColorGreen // 优秀
							case 2:
								previewArray[1] = consts.ColorOrange // 良好
							}
						} else {
							previewArray[0] = consts.HeaderDefaultValue
						}
					}
				}
			}

			// 预习开启状态检查
			if previewArray[0] == consts.HeaderDefaultValue {
				if openInfo, exists := previewOpenData[lessonID]; exists {
					if openInfo.IsOpenPreview == 1 {
						// 检查预习完成状态
						if lessonLuData.IsPreviewFinish == 1 {
							previewArray[0] = "0/0/0"
						} else {
							previewArray[0] = "未提交"
						}
					} else {
						// 预习未开启，显示"-"
						previewArray[0] = consts.HeaderDefaultValue
					}
				}
				previewArray[2] = 0
			}
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, previewArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取预习数据】", "LU: previewCorrectNum, previewParticipateNum, previewTotalNum, isPreviewFinish; iLab兼容; 预习开启状态检查")
	return
}

// GetAttendData 获取到课数据
// 对应PHP中的attend字段
func (s *Format) GetAttendData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"attend_duration"}) {
		return
	}

	// 获取学生课程数据 - attendDuration字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取学生章节请假信息
	lessonStudentData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonStudentData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	studentLeaveData := lessonStudentData.(map[int64]*models.LessonStudent)

	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := lessonInfoData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		// 获取attendDuration
		attendDuration := int64(0)
		if lessonData, ok := luData[lessonID]; ok {
			attendDuration = lessonData.AttendDuration
		}

		// 获取playType
		playType := int64(0)
		if lesson, ok := lessonMap[lessonID]; ok {
			playType = int64(lesson.PlayType)
		}

		attend := FormatDuration(attendDuration)
		if playType == dal.PLAY_TYPE_LUBOKE {
			attend = consts.HeaderDefaultValue
			attendDuration = 0
		}

		// 到课状态码判断逻辑
		attendCode := 0
		leaveSeason := ""

		// 对应PHP: if (!intval($row['attend']))
		if attend == consts.HeaderDefaultValue || attendDuration == 0 {
			// 检查请假状态 - 对应PHP中的preAttend判断
			if studentInfo, ok := studentLeaveData[lessonID]; ok {
				if studentInfo.PreAttend == models.PreAttendLeave {
					attendCode = 3 // 请假
					if studentInfo.ExtData.LeaveSeason != "" {
						leaveSeason = studentInfo.ExtData.LeaveSeason
					}
				} else {
					attendCode = 0 // 未到
				}
			} else {
				attendCode = 0 // 未到
			}
		} else if attendDuration >= 30*60 { // 对应PHP: 30 <= ($attendDutation / 60)
			attendCode = 1 // 到课时长足够30min
		} else {
			attendCode = 2 // 到课时长不足30min
		}

		// 添加到课数据
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attend)
		// 添加到课状态码和请假原因
		_ = s.AddOutputStudent(ctx, lessonID, "attendCode", attendCode)
		_ = s.AddOutputStudent(ctx, lessonID, "leaveSeason", leaveSeason)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【直播到课时长】", "ES: attendDuration, DAL: playType, DB: tblLessonStudent学生请假信息")
	return
}

// GetPlayback 获取回放数据
// 对应PHP中的playback字段，格式化为"XminYs"或"-"（录播课程）
func (s *Format) GetPlayback(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"playback_time", "playback_time_after_unlock"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"playbackTinclass_teacher_room_total_playback_time_v1otalTime"}) {
		return
	}

	// 获取LU数据 - 回放时长相关字段
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	queryData2, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	commonluData := queryData2.(map[int64]*dataproxy.GetCommonLuResp)

	// 获取章节基础信息 - t007Tag和playType字段
	lessonBaseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonBaseInfo", []interface{}{s.param.LessonIDs})
	if err != nil {
		return
	}
	lessonInfoMap := lessonBaseInfo.(map[int64]*achilles.ProcessedLessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		playback := consts.HeaderDefaultValue
		playbackV1 := ""

		if lessonLuData, ok := luData[lessonID]; ok {
			var playbackTotalTime int64

			// 根据t007Tag决定使用哪个回放时长
			if lessonInfo, exists := lessonInfoMap[lessonID]; exists && lessonInfo.T007Tag == 1 {
				playbackTotalTime = lessonLuData.PlaybackTimeAfterUnlock
			} else {
				playbackTotalTime = lessonLuData.PlaybackTotalTime
			}
			// 检查是否为录播课程
			if lessonInfo, exists := lessonInfoMap[lessonID]; exists && lessonInfo.PlayType == dal.PLAY_TYPE_LUBOKE {
				playback = consts.HeaderDefaultValue
			} else {
				// 格式化回放时长
				playback = FormatDuration(playbackTotalTime)
			}
		}

		if commonLessonData, ok := commonluData[lessonID]; ok {
			playbackV1 = FormatDuration(commonLessonData.InclassTeacherRoomTotalPlaybackTimeV1)
		}

		// 输出回放时长
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playback)
		_ = s.AddOutputStudent(ctx, lessonID, "playbackV1", playbackV1)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【回放时长】", "LU: playbackTotalTime, playbackTimeAfterUnlock + Achilles: t007Tag, playType")
	return
}

// GetPlaybackOnlineTimeV1 获取回放观看时长(新)
// 对应PHP中的playbackv1字段，格式化为"XminYs"
func (s *Format) GetPlaybackOnlineTimeV1(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"inclass_teacher_room_total_playback_time_v1"}) {
		return
	}

	// 获取学生课程数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	for _, lessonID := range s.param.LessonIDs {
		playbackV1Data := ""

		if lessonData, ok := luData[lessonID]; ok {
			playbackTime := lessonData.InclassTeacherRoomTotalPlaybackTimeV1
			// 直接格式化时长，与PHP逻辑一致（即使为0也格式化）
			playbackV1Data = FormatDuration(playbackTime)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playbackV1Data)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取回放观看时长(新)】", "ES: inclass_teacher_room_total_playback_time_v1")
	return
}

// GetLbpAttendDuration 获取LBP观看时长
// 对应PHP中的lbpAttendDuration字段，格式化为"XminYs"
func (s *Format) GetLbpAttendDuration(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_teacher_room_total_playback_content_time"}) {
		return
	}

	// 获取公共LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	commonLuData := queryData.(map[int64]*dataproxy.GetCommonLuResp)

	for _, lessonID := range s.param.LessonIDs {
		lbpAttendDuration := ""
		if lessonData, ok := commonLuData[lessonID]; ok {
			lbpAttendDuration = FormatDuration(lessonData.InclassTeacherToomTotalPlaybackContentTime)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpAttendDuration)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP观看时长】", "CommonLU: inclass_teacher_room_total_playback_content_time")
	return
}

// GetLbpAttendDurationOld 获取LBP观看时长(旧版)
// 对应PHP中的lbpAttendDurationOld字段，格式化为"XminYs"
func (s *Format) GetLbpAttendDurationOld(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"lbp_attend_duration"}) {
		return
	}

	// 获取学生课程数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	for _, lessonID := range s.param.LessonIDs {
		lbpAttendDurationOld := ""

		if lessonData, ok := luData[lessonID]; ok {
			// 直接格式化时长，与PHP逻辑一致（即使为0也格式化）
			lbpAttendDurationOld = FormatDuration(lessonData.LbpAttendDuration)
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpAttendDurationOld)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP观看时长(旧版)】", "ES: lbp_attend_duration")
	return
}

// GetOralQuestion 获取口述题数据
// 对应PHP中的oralQuestion字段，包含复杂的状态判断逻辑
func (s *Format) GetOralQuestion(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceExamRelation, []string{"is_artificial_correct"}) {
		return
	}

	// 获取LU数据 - 口述题相关字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取试卷绑定数据 - 口述题绑定信息
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		dataQuery.RelationTypeLesson,
		[]int64{dataQuery.BindTypeOralQuestion},
	})
	if err != nil {
		return
	}
	examRelationData := examData.(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化口述题数据数组：[显示文本, 颜色, 是否可点击]
		oralQuestionArray := LessonDataArray(consts.HeaderDefaultValue, consts.ColorGray, 0)
		status := consts.OralQuStatusUnknown

		// 检查是否有口述题绑定
		if examInfo, exists := examRelationData[lessonID][dataQuery.BindTypeOralQuestion]; exists {
			bindStatus := false
			isArtificialCorrect := false

			if examInfo.BindStatus > 0 {
				bindStatus = true
			}

			if examInfo.IsArtificialCorrect > 0 {
				isArtificialCorrect = true
			}

			if bindStatus {
				// 获取学生口述题数据
				if lessonLuData, exists := luData[lessonID]; exists {
					oralQuestionSubmit := lessonLuData.OralQuestionSubmit > 0
					oralQuestionCorrectTime := lessonLuData.OralQuestionCorrectTime

					if oralQuestionSubmit {
						status = consts.OralQuStatusSubmit
						// 需要批改且没有批改（根据时间判断），则展示待批改
						if isArtificialCorrect && oralQuestionCorrectTime <= 0 {
							status = consts.OralQuStatusTbCorrected
						}
					} else {
						status = consts.OralQuStatusUnsubmit
					}
				} else {
					status = consts.OralQuStatusUnsubmit
				}
			}
		}
		// 设置显示文本
		oralQuestionArray[0] = consts.OralQuStatusMap[status]

		// 设置颜色和可点击状态
		if status == consts.OralQuStatusSubmit || status == consts.OralQuStatusTbCorrected {
			oralQuestionArray[1] = consts.ColorGreen
			oralQuestionArray[2] = 1 // 可点击
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, oralQuestionArray)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取口述题数据】", "LU: oralQuestionSubmit, oralQuestionCorrectTime + Exam: bind_status, is_artificial_correct")
	return
}

// GetInclassTest 获取堂堂测数据
// 对应PHP中的inclassTest字段
func (s *Format) GetInclassTest(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceExamRelation, []string{"total_num"}) {
		return
	}

	// 获取LU数据 - 堂堂测相关字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取考试绑定数据 - 堂堂测总数
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		int(dataQuery.RelationTypeLesson),
		[]int64{int64(dataQuery.BindTypeTestInClass)},
	})
	if err != nil {
		return
	}
	examRelationData := examData.(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)

	// 获取课程信息 - 用于获取年级和学科信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{
		s.param.CourseID,
	})
	if err != nil {
		return
	}
	course := courseInfo.(dal.CourseInfo)
	gradeId := course.MainGradeId
	subjectId := course.MainSubjectId

	var ilab *dataQuery.ILabInfo
	if slices.Contains(consts.AcceptGradeSlice, gradeId) && slices.Contains(consts.AcceptSubjectSlice, subjectId) {
		// 获取ILab信息
		ilabInfo, ilabErr := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{s.param.LessonIDs, s.param.StudentUid})
		if ilabErr != nil {
			return ilabErr
		}
		ilab = ilabInfo.(*dataQuery.ILabInfo)
	}

	for _, lessonID := range s.param.LessonIDs {
		// 初始化堂堂测数据数组：[显示文本, 颜色, 是否可点击]
		inclassTestArray := LessonDataArray(consts.HeaderDefaultValue, consts.ColorGray, 1)

		// 获取堂堂测总数
		tangTangTotalNum := int64(0)
		if examInfo, exists := examRelationData[lessonID][dataQuery.BindTypeTestInClass]; exists {
			if examInfo.TotalNum > 0 && examInfo.BindStatus > 0 {
				tangTangTotalNum = examInfo.TotalNum
			}
		}

		// 获取学生堂堂测数据
		if lessonLuData, exists := luData[lessonID]; exists {
			correctNum := lessonLuData.TangTangExamCorrectNum
			participateNum := lessonLuData.TangTangExamParticipateNum

			inclassTestArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, tangTangTotalNum)

			if tangTangTotalNum > 0 {
				// 设置颜色：满分绿色，不满分橙色
				if correctNum == tangTangTotalNum {
					inclassTestArray[1] = consts.COLOR_GREEN
				} else if correctNum < tangTangTotalNum {
					inclassTestArray[1] = consts.COLOR_ORANGE
				}
			}

			// 处理ILab兼容逻辑（初二物理课程）
			if course.MainGradeId == consts.ILabGradeId && course.MainSubjectId == consts.ILabSubjectId && ilab != nil {
				// 检查是否为ILab章节
				if ilabLessonInfo, exists := ilab.CheckIlabLesson[lessonID]; exists && ilabLessonInfo.ILabLesson > 0 {
					// ILab 逻辑：使用ILab的堂堂测信息
					if ilabInclassTestStatus, exists := ilab.InclassTestInfoByIlab[lessonID]; exists {
						// 有ILab堂堂测状态，根据状态设置显示文本和颜色
						if levelText, exists := consts.ILAB_LEVEL_MAP[ilabInclassTestStatus]; exists {
							inclassTestArray[0] = levelText
							// 根据等级设置颜色
							switch ilabInclassTestStatus {
							case 1:
								inclassTestArray[1] = consts.COLOR_GREEN
							case 2:
								inclassTestArray[1] = consts.COLOR_ORANGE
							}
						}
					}
				}
			}

		}

		// 如果显示为"-"，设置不可点击
		if inclassTestArray[0] == consts.HeaderDefaultValue {
			inclassTestArray[2] = 0
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, inclassTestArray)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取堂堂测数据】", "LU: tangTangExamCorrectNum, tangTangExamParticipateNum, tangTangExamScore, isTangTangExamSubmit + Exam: total_num + CheckIsHx + ILab")
	return
}

// GetHomeworkData 获取作业数据
// 对应PHP中的homework字段，格式化为["状态", "颜色", "是否可点击"]
func (s *Format) GetHomeworkData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) {
		return
	}

	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}

	// 获取DAS数据 - 作业状态和订正状态
	dasQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDasLessonsData", []interface{}{
		[]int64{s.param.StudentUid}, s.param.LessonIDs,
	})
	if err != nil {
		return err
	}

	// 获取巩固练习绑定状态
	hwBindExams, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkBindExams", []interface{}{
		s.param.LessonIDs,
		consts.BindTypeHomework, // 作业绑定类型
	})
	if err != nil {
		return err
	}

	// 判断是否为订正项目课程
	isHac, err := s.dataQueryPoint.GetInstanceData(ctx, "IsHomeworkAmendCourse", []interface{}{s.param.CourseID})
	if err != nil {
		return err
	}

	// 获取作业开启信息
	homeworkOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkOpenInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return err
	}

	// 获取课程信息 - 用于获取年级和学科信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{
		s.param.CourseID,
	})
	if err != nil {
		return err
	}

	// 获取ILab信息（如果是初二物理）
	ilabInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetILabInfo", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 获取需要审核的课程映射
	lessonNeedAuditMap, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonNeedAuditMap", []interface{}{s.param.LessonIDs})
	if err != nil {
		return err
	}

	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		dataQuery.RelationTypeLesson,
		[]int64{dataQuery.BindTypeHomework},
	})
	if err != nil {
		return
	}

	// 类型转换
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)
	dasData := dasQueryData.(map[int64]map[int64]*das.StudentLessonInfo)
	bindExams := hwBindExams.(map[int64]bool)
	openInfo := homeworkOpenInfo.(map[int64]jxexamui.HomeworkOpenInfo)
	ilab := ilabInfo.(*dataQuery.ILabInfo)
	course := courseInfo.(dal.CourseInfo)
	isAmendCourse := isHac.(bool)
	auditMap := lessonNeedAuditMap.(map[int64]bool)
	examRelationData := examData.(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)

	isHomeworkOpen := openInfo[s.param.CourseID].IsOpen == consts.HomeworkOpen

	for _, lessonID := range s.param.LessonIDs {
		// 初始化作业数据数组：[显示文本, 颜色, 是否可点击]
		homeworkArray := LessonDataArray(consts.HeaderDefaultValue, consts.ColorGray, 1)

		// 获取当前章节的数据
		dasStudentLessonInfo := dasData[s.param.StudentUid][lessonID]
		isBindHw := bindExams[lessonID]
		luData := luData[lessonID]

		// 兼容das作业未布置错误问题
		if dasStudentLessonInfo != nil && dasStudentLessonInfo.HomeworkStatus == 0 && isBindHw {
			dasStudentLessonInfo.HomeworkStatus = 1
		}

		// 使用consts包中的作业等级映射
		homeworkLevelMap := consts.HomeworkLevelMap

		// 处理订正课程逻辑
		if isAmendCourse {
			// 检查作业是否开启
			if !isHomeworkOpen {
				homeworkArray[0] = consts.StatusDash
			} else if !isBindHw {
				homeworkArray[0] = consts.StatusNotAssigned
			} else if luData != nil && luData.HomeworkLevel > 0 {
				// 有作业等级，显示等级
				if levelText, exists := homeworkLevelMap[luData.HomeworkLevel]; exists {
					homeworkArray[0] = levelText
				} else {
					homeworkArray[0] = consts.StatusNoLevel
				}
			} else if dasStudentLessonInfo != nil {
				// 处理订正状态
				switch dasStudentLessonInfo.HomeworkRecorrect {
				case 2:
					homeworkArray[0] = consts.StatusWaitGrade
					// 检查是否需要审核
					if auditMap[lessonID] {
						homeworkArray[0] = consts.StatusWaitAudit
					}
				case 4:
					homeworkArray[0] = consts.StatusWaitRegrade
				case 5:
					homeworkArray[0] = consts.StatusWaitResubmit
				default:
					homeworkArray[0] = consts.StatusNotSubmitted
				}
			}
		} else {
			// 检查作业是否开启
			if !isHomeworkOpen {
				homeworkArray[0] = consts.StatusDash
			} else if !isBindHw {
				homeworkArray[0] = consts.StatusNotAssigned
			} else if dasStudentLessonInfo != nil {
				switch dasStudentLessonInfo.HomeworkStatus {
				case 1:
					homeworkArray[0] = consts.StatusNotSubmitted
				case 2:
					homeworkArray[0] = consts.StatusNotGraded
				case 3:
					// 已批改，显示等级
					if luData != nil && luData.HomeworkLevel > 0 {
						if levelText, exists := homeworkLevelMap[luData.HomeworkLevel]; exists {
							homeworkArray[0] = levelText
						} else {
							homeworkArray[0] = consts.StatusNoLevel
						}
					} else {
						homeworkArray[0] = consts.StatusNoLevel
					}
				default:
					homeworkArray[0] = consts.StatusDash
				}
			}
		}

		// 设置颜色
		if homeworkArray[0] == consts.HomeworkFullMarksCode {
			homeworkArray[1] = consts.ColorGreen // 满分
		} else {
			// 检查是否为其他等级（不满分）
			for _, levelText := range homeworkLevelMap {
				if homeworkArray[0] == levelText && homeworkArray[0] != consts.HomeworkFullMarksCode {
					homeworkArray[1] = consts.ColorOrange // 不满分
					break
				}
			}
		}

		// 处理ILab兼容逻辑（初二物理课程）
		if course.MainGradeId == consts.ILabGradeId && course.MainSubjectId == consts.ILabSubjectId && ilab != nil {
			if ilabLessonInfo, exists := ilab.CheckIlabLesson[lessonID]; exists {
				// ILab v2 逻辑：使用ILab的作业信息
				if ilabLessonInfo.Version == consts.ILabVersion2 {
					if ilabHomeworkStatus, exists := ilab.HomeworkInfoByIlab[lessonID]; exists && ilabHomeworkStatus > 0 {
						homeworkArray[0] = consts.ILAB_LEVEL_MAP[ilabHomeworkStatus]
						// 根据等级设置颜色
						switch ilabHomeworkStatus {
						case 1:
							homeworkArray[1] = consts.COLOR_GREEN
						case 2:
							homeworkArray[1] = consts.COLOR_ORANGE
						}
					} else {
						homeworkArray[0] = consts.HeaderDefaultValue
					}
				}

				if ilabLessonInfo.Version == consts.ILabVersion1 {
					if ilabHomeworkStatus, exists := ilab.HomeworkInfoByNomal[lessonID]; exists && ilabHomeworkStatus > 0 {
						homeworkArray[0] = consts.ILAB_LEVEL_MAP[ilabHomeworkStatus]
						// 根据等级设置颜色
						switch ilabHomeworkStatus {
						case 1:
							homeworkArray[1] = consts.COLOR_GREEN
						case 2:
							homeworkArray[1] = consts.COLOR_ORANGE
						}
					} else {
						homeworkArray[0] = consts.HeaderDefaultValue
					}
				}
			}
		}

		// 设置不可点击状态
		if homeworkArray[0] == consts.HeaderDefaultValue {
			homeworkArray[2] = 0
		}

		var homeworkNums string
		homeworkTotalNum := int64(0)
		if lessonData, exists := examRelationData[lessonID]; exists {
			if homeworkData := lessonData[dataQuery.BindTypeHomework]; homeworkData != nil {
				homeworkTotalNum = homeworkData.TotalNum
			}
		}

		if homeworkTotalNum > 0 {
			homeworkNums = fmt.Sprintf(consts.LessonExerciseDetail,
				luData.HomeworkPracticeCorrectNum,
				luData.HomeworkPracticeParticipateNum,
				homeworkTotalNum)
		} else {
			homeworkNums = consts.HeaderDefaultValue
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, homeworkArray)
		_ = s.AddOutputStudent(ctx, lessonID, "homeworkNums", homeworkNums)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【获取作业数据】", "DAS: homeworkStatus + LU: homeworkLevel + 作业绑定数据 + 开启状态 + ILab")
	return
}

// GetHomeworkLikeData 获取相似题数据
// 对应PHP中的similarHomework字段
func (s *Format) GetHomeworkLikeData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) {
		return
	}

	// 获取LU数据 - 相似题字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 获取作业绑定数据
	hwBindExams, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkBindExams", []interface{}{
		s.param.LessonIDs,
		consts.BindTypeHomework, // 作业绑定类型
	})
	if err != nil {
		return err
	}

	// 获取作业开启信息
	homeworkOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkOpenInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return err
	}

	// 类型转换
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)
	bindExams := hwBindExams.(map[int64]bool)
	openInfo := homeworkOpenInfo.(map[int64]jxexamui.HomeworkOpenInfo)
	isHomeworkOpen := openInfo[s.param.CourseID].IsOpen == consts.HomeworkOpen

	// 处理每个章节的相似题数据
	for _, lessonID := range s.param.LessonIDs {
		similarHomeworkArray := []interface{}{consts.HeaderDefaultValue, consts.ColorGray, 1}

		// 获取当前章节的LU数据
		lessonLuData, hasLuData := luData[lessonID]
		isBindHw := bindExams[lessonID]

		if isHomeworkOpen && isBindHw && hasLuData {
			// 获取相似题数据 - 使用点分隔的字段名直接访问
			var correctLevel, correctStatus int

			// 从原始map中获取exam_answer.exam33.correct_level字段
			if correctLevelInterface, exists := lessonLuData.ExamAnswer["exam33"]["correct_level"]; exists {
				cast.ToInt(correctLevelInterface)
			}

			// 从原始map中获取exam_answer.exam33.last_correct_status字段
			if correctStatusInterface, exists := lessonLuData.ExamAnswer["exam33"]["last_correct_status"]; exists {
				cast.ToInt(correctStatusInterface)
			}

			// 检查是否在等待显示状态中
			if consts.ExamCorrectStatusWaitShowMap[correctStatus] {
				// 显示订正状态文本
				if statusText, exists := consts.ExamCorrectStatusMap[correctStatus]; exists {
					similarHomeworkArray[0] = statusText
				}
			} else {
				// 显示等级
				if levelText, exists := consts.HomeworkLevelMap[int64(correctLevel)]; exists && correctLevel > 0 {
					similarHomeworkArray[0] = levelText
					similarHomeworkArray[1] = consts.ColorOrange // 不满分
				} else {
					similarHomeworkArray[0] = consts.StatusNoLevel
				}
			}

			// 检查是否为满分
			if similarHomeworkArray[0] == consts.HomeworkFullMarksCode {
				similarHomeworkArray[1] = consts.ColorGreen // 满分
			}
		} else {
			// 作业未开启或未绑定，设置不可点击
			similarHomeworkArray[2] = 0
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, similarHomeworkArray)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取相似题数据】", "LU: exam_answer.exam33.* + 作业绑定数据 + 作业开启状态")
	return
}

// GetExerciseColumn 获取互动题数据
// 对应PHP中的exercise字段，格式化为"正确数|参与数|总数"
func (s *Format) GetExerciseColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_right_cnt", "inclass_participate_cnt", "inclass_question_cnt"}) {
		return
	}

	// 获取DAS学生章节数据
	dasLessonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDasLessonsData", []interface{}{
		[]int64{s.param.StudentUid},
		s.param.LessonIDs,
	})
	if err != nil {
		return err
	}

	// 获取LU通用数据
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 类型转换
	dasData := dasLessonData.(map[int64]map[int64]*das.StudentLessonInfo)
	luData := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	// 处理每个章节的互动题数据
	for _, lessonID := range s.param.LessonIDs {
		// 获取当前章节的数据
		isAttended := false
		if studentData, exists := dasData[s.param.StudentUid]; exists {
			if lessonData, exists := studentData[lessonID]; exists {
				isAttended = lessonData.IsAttended > 0
			}
		}

		intInteractTotalNum := int64(0)
		inClassRightCnt := int64(0)
		inClassParticipateCnt := int64(0)
		if lessonLuData, exists := luData[lessonID]; exists {
			intInteractTotalNum = lessonLuData.InclassQuestionCnt
			inClassRightCnt = lessonLuData.InclassRightCnt
			inClassParticipateCnt = lessonLuData.InclassParticipateCnt
		}

		// 格式化互动题数据
		exercise := fmt.Sprintf(consts.LessonExerciseDetail,
			inClassRightCnt,
			inClassParticipateCnt,
			intInteractTotalNum)

		exerciseCode := 0
		if !isAttended {
			exerciseCode = consts.ExerciseCodeNotParticipated
		} else if intInteractTotalNum > 0 && inClassRightCnt == intInteractTotalNum {
			exerciseCode = consts.ExerciseCodeAllCorrect
		} else {
			exerciseCode = consts.ExerciseCodeNotAllCorrect
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exercise)
		_ = s.AddOutputStudent(ctx, lessonID, "exerciseCode", exerciseCode)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取互动题数据】", "DAS: isAttended + LU: inclass_* + 考试绑定数据 + 灰度测试")
	return
}

// GetExerciseAllColumn 获取观看互动题数据（课中+回放）
// 对应PHP中的exerciseAll字段，格式化为"(课中正确数+回放正确数)|(课中参与数+回放参与数)|总数"
func (s *Format) GetExerciseAllColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_right_cnt", "inclass_participate_cnt", "inclass_question_cnt", "playback_right_cnt", "playback_participate_cnt"}) {
		return
	}

	// 获取LU通用数据（包含课中和回放数据）
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 类型转换
	luData := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	// 处理每个章节的观看互动题数据
	for _, lessonID := range s.param.LessonIDs {
		exerciseAll := consts.HeaderDefaultValue
		intInteractTotalNum := int64(0)
		inClassRightCnt := int64(0)
		inClassParticipateCnt := int64(0)
		playbackRightCnt := int64(0)
		playbackParticipateCnt := int64(0)

		if lessonLuData, exists := luData[lessonID]; exists {
			intInteractTotalNum = lessonLuData.InclassQuestionCnt
			inClassRightCnt = lessonLuData.InclassRightCnt
			inClassParticipateCnt = lessonLuData.InclassParticipateCnt
			playbackRightCnt = lessonLuData.PlaybackRightCnt
			playbackParticipateCnt = lessonLuData.PlaybackParticipateCnt
		}

		if intInteractTotalNum > 0 {
			// 格式化观看互动题数据（课中+回放）
			exerciseAll = fmt.Sprintf(consts.LessonExerciseDetail,
				inClassRightCnt+playbackRightCnt,
				inClassParticipateCnt+playbackParticipateCnt,
				intInteractTotalNum)
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, exerciseAll)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取观看互动题数据】", "LU: inclass_* + playback_* + 考试绑定数据 + 灰度测试")
	return
}

// GetLbpInteractExamColumn 获取LBP互动题数据
// 对应PHP中的lbpInteractExam字段，格式化为"正确数|提交数|总数"
func (s *Format) GetLbpInteractExamColumn(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"mix_live_interaction_right_num", "mix_live_interaction_submit_num"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_question_cnt"}) {
		return
	}

	// 获取LU数据（包含LBP互动题数据）
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 获取LU通用数据
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 类型转换
	luStudentDataMap := luData.(map[int64]*dataproxy.GetLuDataResp)
	luCommonDataMap := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	// 处理每个章节的LBP互动题数据
	for _, lessonID := range s.param.LessonIDs {
		lbpInteractExam := consts.HeaderDefaultValue

		intInteractTotalNum := int64(0)
		if commonData, exists := luCommonDataMap[lessonID]; exists {
			intInteractTotalNum = commonData.InclassQuestionCnt
		}

		mixLiveInteractionRightNum := int64(0)
		mixLiveInteractionSubmitNum := int64(0)
		if luStudentData, exists := luStudentDataMap[lessonID]; exists {
			mixLiveInteractionRightNum = luStudentData.MixLiveInteractionRightNum
			mixLiveInteractionSubmitNum = luStudentData.MixLiveInteractionSubmitNum
		}

		if intInteractTotalNum > 0 {
			// 格式化LBP互动题数据
			lbpInteractExam = fmt.Sprintf(consts.LessonExerciseDetail,
				mixLiveInteractionRightNum,
				mixLiveInteractionSubmitNum,
				intInteractTotalNum)
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, lbpInteractExam)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取LBP互动题数据】", "LU: mix_live_interaction")
	return
}

// GetMixPlaybackInteract 获取融合回放互动题数据
// 对应PHP中的mixPlaybackInteract字段
func (s *Format) GetMixPlaybackInteract(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"mix_playback_interaction_right_num", "mix_playback_interaction_submit_num"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLesson, []string{"mix_interaction_total_num"}) {
		return
	}

	// 获取LU数据（包含融合回放互动题数据）
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 获取章节数据（获取总数）
	lessonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonDataByLessonIds", []interface{}{s.param.LessonIDs})
	if err != nil {
		return err
	}

	// 类型转换
	luStudentDataMap := luData.(map[int64]*dataproxy.GetLuDataResp)
	lessonDataMap := lessonData.(map[int64]*dataproxy.GetLessonDataByLessonIdsResp)

	// 处理每个章节的融合回放互动题数据
	for _, lessonID := range s.param.LessonIDs {
		mixPlaybackInteract := consts.HeaderDefaultValue

		mixPlaybackInteractionRightNum := int64(0)
		mixPlaybackInteractionSubmitNum := int64(0)
		if luStudentData, exists := luStudentDataMap[lessonID]; exists {
			mixPlaybackInteractionRightNum = luStudentData.MixPlaybackInteractionRightNum
			mixPlaybackInteractionSubmitNum = luStudentData.MixPlaybackInteractionSubmitNum
		}

		// 获取总数
		totalNum := int64(0)
		if lessonInfo, exists := lessonDataMap[lessonID]; exists {
			totalNum = lessonInfo.MixInteractionTotalNum
		}

		if totalNum > 0 {
			// 格式化融合回放互动题数据
			mixPlaybackInteract = fmt.Sprintf(consts.LessonExerciseDetail,
				mixPlaybackInteractionRightNum,
				mixPlaybackInteractionSubmitNum,
				totalNum)
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, mixPlaybackInteract)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【获取融合回放互动题数据】", "LU: mix_playback_interaction_* + 章节数据: mix_interaction_total_num")
	return
}

// GetLittleKidFudaoData 获取小鹿辅导作业数据
// 对应PHP中的getLittleKidFudaoData方法，处理littleKidFudaoHomeworkStatus和littleKidFudaoHomeworkLevel字段
func (s *Format) GetLittleKidFudaoData(ctx *gin.Context) (err error) {
	// 添加需要的字段到数据查询
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"deer_programming_homework_level", "deer_programming_homework_status"}) {
		return
	}

	// 获取LU数据
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return err
	}

	// 类型转换
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 处理每个章节的小鹿辅导作业数据
	for _, lessonID := range s.param.LessonIDs {
		// 获取当前章节的LU数据
		lessonLuData, hasLuData := luData[lessonID]

		var homeworkStatus, homeworkLevel string

		if hasLuData {
			// 获取小鹿编程作业状态（现在已经是字符串类型）
			statusValue := lessonLuData.DeerProgrammingHomeworkStatus

			// 根据PHP中的statusMap进行映射
			if mappedStatus, exists := consts.DeerProgrammingHomeworkStatusMap[statusValue]; exists {
				homeworkStatus = mappedStatus
			} else {
				homeworkStatus = consts.DeerProgrammingHomeworkStatusMap[""] // 默认为"未提交"
			}

			// 获取小鹿编程作业等级
			levelValue := int(lessonLuData.DeerProgrammingHomeworkLevel)

			// 根据PHP中的levelMap进行映射
			if mappedLevel, exists := consts.DeerProgrammingHomeworkLevelMap[levelValue]; exists {
				homeworkLevel = mappedLevel
			} else {
				homeworkLevel = consts.DeerProgrammingHomeworkLevelMap[0] // 默认为空字符串
			}
		} else {
			homeworkStatus = consts.DeerProgrammingHomeworkStatusMap[""] // 默认为"未提交"
			homeworkLevel = consts.DeerProgrammingHomeworkLevelMap[0]    // 默认为空字符串
		}

		_ = s.AddOutputStudent(ctx, lessonID, "littleKidFudaoHomeworkStatus", homeworkStatus)
		_ = s.AddOutputStudent(ctx, lessonID, "littleKidFudaoHomeworkLevel", homeworkLevel)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【小鹿辅导作业】", "ES: idl_assistant_lesson_student_action 字段: deer_programming_homework_*")

	return
}

// GetLittleKidInteractData 获取小鹿辅导互动题对答总数据
// 对应PHP中的getLittleKidInteractData方法，处理littleKidFudaoInteract字段
func (s *Format) GetLittleKidInteractData(ctx *gin.Context) (err error) {
	// 添加需要的字段到数据查询
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"inclass_right_cnt", "inclass_participate_cnt"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceExamRelation, []string{"total_num"}) {
		return
	}

	// 获取LU Common数据
	luCommonQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return err
	}
	luCommonData := luCommonQueryData.(map[int64]*dataproxy.GetCommonLuResp)

	// 获取考试绑定数据 - 互动题总数
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		dataQuery.RelationTypeLesson,
		[]int64{dataQuery.BindTypePracticeInClass},
	})
	if err != nil {
		return err
	}
	examRelationData := examData.(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)

	// 遍历每个章节，计算小鹿辅导互动题对答总
	for _, lessonID := range s.param.LessonIDs {
		// 获取当前章节的LU Common数据
		lessonLuCommonData, exists := luCommonData[lessonID]
		if !exists {
			lessonLuCommonData = &dataproxy.GetCommonLuResp{}
		}

		// 获取互动题总数
		var intInteractTotalNum int64 = 0
		if lessonExamData, exists := examRelationData[lessonID]; exists {
			if practiceData, exists := lessonExamData[dataQuery.BindTypePracticeInClass]; exists {
				intInteractTotalNum = practiceData.TotalNum
			}
		}

		// 获取正确数和参与数
		rightNum := lessonLuCommonData.InclassRightCnt
		participateNum := lessonLuCommonData.InclassParticipateCnt

		// 格式化输出
		var result string
		if intInteractTotalNum == 0 {
			result = "-"
		} else {
			result = fmt.Sprintf("%d/%d/%d", rightNum, participateNum, intInteractTotalNum)
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, result)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【小鹿辅导互动题对答总】", "es:dataware_idl_common_lesson_student 字段：inclass_right_cnt,inclass_participate_cnt 总数 码表totalNum字段")
	return
}

// GetSynchronousPractice 获取同步练习数据
// 对应PHP中的synchronousPractice字段
func (s *Format) GetSynchronousPractice(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) &&
		s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceExamRelation, []string{"total_num", "bind_status"}) {
		return
	}

	// 获取LU数据 - 同步练习相关字段
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取考试绑定数据 - 同步练习总数
	examData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetExamRelationData", []interface{}{
		s.param.LessonIDs,
		dataQuery.RelationTypeLesson,
		[]int64{int64(dataQuery.BindTypePrimaryMathPreview)},
	})
	if err != nil {
		return
	}
	examRelationData := examData.(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)

	for _, lessonID := range s.param.LessonIDs {
		// 初始化同步练习数据为默认值
		synchronousPracticeResult := consts.HeaderDefaultValue

		// 检查是否有同步练习绑定数据
		if examInfo, exists := examRelationData[lessonID][dataQuery.BindTypePrimaryMathPreview]; exists {
			// 检查绑定状态和总数是否有效
			if examInfo.BindStatus > 0 && examInfo.TotalNum > 0 {
				// 获取学生同步练习数据
				if lessonLuData, luExists := luData[lessonID]; luExists {
					correctNum := lessonLuData.SynchronousPracticeCorrectNum
					participateNum := lessonLuData.SynchronousPracticeParticipateNum
					totalNum := examInfo.TotalNum

					// 格式化为"正确数|参与数|总数"
					synchronousPracticeResult = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, totalNum)
				}
			}
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, synchronousPracticeResult)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【同步练习数据】", "bdl_exam_relation确定总数，lu中synchronousPracticeCorrectNum和synchronousPracticeParticipateNum为正确数和参与数")
	return
}

// GetHasCompositionReportData 获取是否有作文报告数据
// 对应PHP中的hasCompositionReport字段
func (s *Format) GetHasCompositionReportData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"exam_answer"}) {
		return
	}

	// 获取LU数据 - 巩固练习等级
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取课程基础信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)

	// 检查年级和学科是否符合条件：(小学 && 语文) 或 (初中 && 英语)
	var compositionConf map[int64]*pcassistant.CompositionConf
	gradeStage := consts.GetDepartmentIdByGradeId(courseData.MainGradeId)

	if (gradeStage == define.GradeStagePrimary && courseData.MainSubjectId == consts.SubjectChinese) ||
		(gradeStage == define.GradeStageJunior && courseData.MainSubjectId == consts.SubjectEnglish) {
		// 获取作文配置信息
		compositionData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCompositionConf", []interface{}{s.param.LessonIDs})
		if err != nil {
			return err
		}
		compositionConf = compositionData.(map[int64]*pcassistant.CompositionConf)
	}

	for _, lessonID := range s.param.LessonIDs {
		result := 0

		// 检查是否有作文配置且tidList不为空
		if compositionConf != nil {
			if conf, exists := compositionConf[lessonID]; exists && len(conf.TidList) > 0 {
				// 检查学生是否有巩固练习等级
				if lessonLuData, luExists := luData[lessonID]; luExists && lessonLuData.HomeworkLevel > 0 {
					result = 1
				}
			}
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, result)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【巩固练习作文】", "lu中有homeworkLevel，批改有巩固练习作文配置：/pcassistant/api/composition/getcompositiontid")
	return
}

// GetTalk 获取课中聊天数据
// 对应PHP中的talk字段
func (s *Format) GetTalk(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"chat_num"}) {
		return
	}

	// 获取LU数据 - 聊天次数
	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取DAS数据 - 到课状态
	dasData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDasLessonsData", []interface{}{[]int64{s.param.StudentUid}, s.param.LessonIDs})
	if err != nil {
		return
	}
	dasStudentLessonData := dasData.(map[int64]map[int64]*das.StudentLessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		chatNum := int64(0)
		talkCode := 0

		// 获取聊天次数
		if lessonLuData, luExists := luData[lessonID]; luExists {
			chatNum = lessonLuData.ChatNum
		}

		// 获取到课状态，决定聊天颜色代码
		if studentData, studentExists := dasStudentLessonData[s.param.StudentUid]; studentExists {
			if lessonDasData, lessonExists := studentData[lessonID]; lessonExists {
				if lessonDasData.IsAttended == 1 {
					talkCode = 1 // 已到课
				} else {
					talkCode = 0 // 未到课
				}
			}
		}

		// 输出聊天次数
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, chatNum)
		_ = s.AddOutputStudent(ctx, lessonID, "talkCode", talkCode)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【课中聊天】", "das中isAttended确定是否聊天，lu中chatNum确定聊天数")
	return
}

// GetScoreData 获取学分数据
// 对应PHP中的score字段
func (s *Format) GetScoreData(ctx *gin.Context) (err error) {
	// 获取学分数据
	scoreData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetStudentLessonsScore", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	scoreMap := scoreData.(map[int64]*jxdascore.StudentLessonScore)

	for _, lessonID := range s.param.LessonIDs {
		score := 0
		if scoreInfo, ok := scoreMap[lessonID]; ok {
			score = scoreInfo.Score
		}
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, score)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【学分】", "学分为教学接口返回：/jxdascore/score/getobjbizscorelist")
	return
}

// GetMonthlyExamReportUrl 获取月考报告URL
// 对应PHP中的getMonthlyExamReportUrl方法
func (s *Format) GetMonthlyExamReportUrl(ctx *gin.Context) (err error) {
	// 获取LU数据 - 堂堂测提交状态
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"isTangTangExamSubmit"}) {
		return
	}

	// 获取月考报告配置数据
	monthlyExamReportLessons, err := s.dataQueryPoint.GetInstanceData(ctx, "GetMonthlyExamReportLessons", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	monthlyExamReportMap := monthlyExamReportLessons.(map[int64]bool)

	luQueryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := luQueryData.(map[int64]*dataproxy.GetLuDataResp)

	// 获取课程信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)
	mainSubjectID := courseData.MainSubjectId

	for _, lessonID := range s.param.LessonIDs {
		monthlyExamReportUrl := ""

		// 检查该课程是否配置了月考报告
		if _, hasConfig := monthlyExamReportMap[lessonID]; hasConfig {
			// 检查学生是否已提交堂堂测
			if lessonLuData, ok := luData[lessonID]; ok && lessonLuData.IsTangTangExamSubmit > 0 {
				// 生成月考报告URL
				reportUrl, urlErr := GenerateMonthlyExamReportUrl(ctx, s.param.StudentUid, s.param.CourseID, lessonID, mainSubjectID)
				if urlErr == nil {
					monthlyExamReportUrl = reportUrl
				}
			}
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, monthlyExamReportUrl)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【月考报告URL】", "月考报告是否配置：tblAssistantMonthExamConf，lu中isTangTangExamSubmit为1再拼接生成月考报告url")
	return
}

// GetIsInclassTeacherRoomAttend30minute 获取是否到课30分钟
// 对应PHP中的isInclassTeacherRoomAttend30minute字段，返回"已到课"或"未到课"
func (s *Format) GetIsInclassTeacherRoomAttend30minute(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"is_inclass_teacher_room_attend_30minute"}) {
		return
	}

	// 获取公共LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	commonLuData := queryData.(map[int64]*dataproxy.GetCommonLuResp)

	for _, lessonID := range s.param.LessonIDs {
		attendStatus := consts.NotAttendStr
		if lessonData, ok := commonLuData[lessonID]; ok && lessonData.IsInclassTeacherRoomAttend30minute == 1 {
			attendStatus = consts.IsAttendStr
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attendStatus)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【到课情况（直播章节）】", "公共数仓lu：is_inclass_teacher_room_attend_30minute")
	return
}

// GetIsAttendFinish 获取是否完课
// 对应PHP中的isAttendFinish字段，返回"已完课"或"未完课"
func (s *Format) GetIsAttendFinish(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"is_attend_finish"}) {
		return
	}

	// 获取LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLuData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	luData := queryData.(map[int64]*dataproxy.GetLuDataResp)

	for _, lessonID := range s.param.LessonIDs {
		finishStatus := consts.NotFinishStr
		if lessonData, ok := luData[lessonID]; ok && lessonData.IsAttendFinish == 1 {
			finishStatus = consts.IsFinishStr
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, finishStatus)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【到课情况（直播章节）】", "辅导数仓lu：is_attend_finish")
	return
}

// GetGjkAttendLessonLubo 获取国际课录播到课状态
// 对应PHP中的gjkAttendLessonLubo字段，返回"已到课"或"未到课"
func (s *Format) GetGjkAttendLessonLubo(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"is_inclass_teacher_room_total_playback_content_time_ge_3min"}) {
		return
	}

	// 获取公共LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	commonLuData := queryData.(map[int64]*dataproxy.GetCommonLuResp)

	for _, lessonID := range s.param.LessonIDs {
		attendStatus := consts.NotAttendStr
		if lessonData, ok := commonLuData[lessonID]; ok && lessonData.IsInclassTeacherRoomTotalPlaybackContentTimeGe3min == 1 {
			attendStatus = consts.IsAttendStr
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attendStatus)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【到课情况（录播章节）】", "公共数仓lu：is_inclass_teacher_room_total_playback_content_time_ge_3min")
	return
}

// GetGjkCompleteLessonLubo 获取国际课录播完课状态
// 对应PHP中的gjkCompleteLessonLubo字段，返回"已完课"或"未完课"
func (s *Format) GetGjkCompleteLessonLubo(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"is_inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration"}) {
		return
	}

	// 获取公共LU数据
	queryData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	commonLuData := queryData.(map[int64]*dataproxy.GetCommonLuResp)

	for _, lessonID := range s.param.LessonIDs {
		finishStatus := consts.NotFinishStr
		if lessonData, ok := commonLuData[lessonID]; ok && lessonData.IsInclassTeacherRoomTotalPlaybackContentTimeEqLessonVideoDuration == 1 {
			finishStatus = consts.IsFinishStr
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, finishStatus)
	}
	_ = s.AddDataSource(ctx, s.rule.Key, "【到课情况（直播章节）】", "公共数仓lu：is_inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration")
	return
}

// GetGjkLessonTag 获取高价课标签数据
// 对应PHP中的gjkLessonTag字段
func (s *Format) GetGjkLessonTag(ctx *gin.Context) (err error) {
	// 获取课程信息（包含播放类型）
	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonInfoMap := lessonInfoData.(map[int64]dal.LessonInfo)

	// 获取学习计划数据
	learningPlanData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLearningPlansData", []interface{}{s.param.StudentUid, s.param.CourseID})
	if err != nil {
		return
	}
	lessonTagMap := learningPlanData.(map[int64]int)

	for _, lessonID := range s.param.LessonIDs {
		canEditPlan := 0
		studyPlanTag := 0

		// 获取播放类型
		playType := 0
		if lessonInfo, exists := lessonInfoMap[lessonID]; exists {
			playType = lessonInfo.PlayType
		}

		// 检查是否为录播类型
		if playType == dal.PLAY_TYPE_LUBOKE {
			// 检查学习计划数据
			if tiers, exists := lessonTagMap[lessonID]; exists {
				canEditPlan = 1
				studyPlanTag = tiers
			}
		}

		_ = s.AddOutputStudent(ctx, lessonID, "canEditPlan", canEditPlan)
		// 使用GJKTagMap将数值转换为文字描述
		studyPlanTagText := consts.GJKTagMap[studyPlanTag]
		_ = s.AddOutputStudent(ctx, lessonID, "studyPlanTag", studyPlanTagText)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【高价课标签】", "存在学习计划并且录入分数后可以获取到标签，可以编辑")
	return
}

// GetLpcLessonName 获取LPC章节名称
// 对应PHP中的lpclessonName字段
func (s *Format) GetLpcLessonName(ctx *gin.Context) (err error) {
	// 获取课程信息（包含章节名称）
	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonInfoMap := lessonInfoData.(map[int64]dal.LessonInfo)

	for _, lessonID := range s.param.LessonIDs {
		lessonName := "-"

		// 获取章节名称
		if lessonInfo, exists := lessonInfoMap[lessonID]; exists {
			if lessonInfo.LessonName != "" {
				lessonName = lessonInfo.LessonName
			}
		}

		// 格式化：章节名称[章节ID]
		formattedName := fmt.Sprintf("%s[%d]", lessonName, lessonID)

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, formattedName)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC章节名称】", "dal courseinfo")
	return
}

// GetLpcTeacherName 获取LPC教师名称
// 对应PHP中的teacherName字段
func (s *Format) GetLpcTeacherName(ctx *gin.Context) (err error) {
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)

	lessonTeacherMap := make(map[int64]string)
	if dal.IsLpcByCourse(ctx, courseData) {
		lessonTeacherData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonTeacherMap", []interface{}{s.param.LessonIDs})
		if err != nil {
			return err
		}
		lessonTeacherMap = lessonTeacherData.(map[int64]string)
	}

	for _, lessonID := range s.param.LessonIDs {
		teacherName := "-"

		// 获取教师名称
		if name, exists := lessonTeacherMap[lessonID]; exists {
			teacherName = name
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, teacherName)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC教师名称】", "dat teacherlesson + dau teacher")
	return
}

// GetLpcAttendStatus 获取LPC到课状态
// 对应PHP中的attendStatus字段
func (s *Format) GetLpcAttendStatus(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLpcLu, []string{"attend", "is_lbp_attend", "is_ai_attend"}) {
		return
	}

	// 获取课程信息（包含开始时间和播放类型）
	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonInfoMap := lessonInfoData.(map[int64]dal.LessonInfo)

	// 获取LU数据（包含到课相关字段）
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLpcStudentData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	lpcLuDataMap := luData.(map[int64]*dataproxy.GetLpcListByCourseStudentResp)

	currentTime := time.Now().Unix()

	for _, lessonID := range s.param.LessonIDs {
		attendStatus := consts.LpcUndoneStatus

		// 获取章节信息
		lessonInfo, lessonExists := lessonInfoMap[lessonID]
		if !lessonExists {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attendStatus)
			continue
		}

		// 检查章节是否还未开始
		if int64(lessonInfo.StartTime) > currentTime {
			attendStatus = consts.LpcWaitStatus
		} else {
			// 获取LU数据
			lu, luExists := lpcLuDataMap[lessonID]
			if luExists {
				// 根据播放类型判断到课状态
				isAttend := int64(0)
				switch lessonInfo.PlayType {
				case dal.PLAY_TYPE_LUBOKE:
					isAttend = lu.IsLbpAttend
				case dal.PLAY_TYPE_AI_HUDONG, dal.PLAY_TYPE_AI_INTERACT:
					isAttend = lu.IsAiAttend
				default:
					isAttend = lu.Attend
				}
				if isAttend > 0 {
					attendStatus = consts.LpcSuccessStatus
				}
			}
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, attendStatus)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC到课状态】", "章节开始时间大于当前时间且根据章节类型确定lu中is_lbp_attend，is_ai_attend，attend某一个为1")
	return
}

// GetLpcFinishStatus 获取LPC完课状态
// 对应PHP中的finishStatus字段
func (s *Format) GetLpcFinishStatus(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLpcLu, []string{"is_lbp_attend_finish", "is_ai_finish", "is_attend_finish"}) {
		return
	}

	// 获取课程信息（包含开始时间和播放类型）
	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonInfoMap := lessonInfoData.(map[int64]dal.LessonInfo)

	// 获取LU数据（包含完课相关字段）
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLpcStudentData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	lpcLuDataMap := luData.(map[int64]*dataproxy.GetLpcListByCourseStudentResp)

	currentTime := time.Now().Unix()

	for _, lessonID := range s.param.LessonIDs {
		finishStatus := consts.LpcUndoneStatus

		// 获取章节信息
		lessonInfo, lessonExists := lessonInfoMap[lessonID]
		if !lessonExists {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, finishStatus)
			continue
		}

		// 检查章节是否还未开始
		if int64(lessonInfo.StartTime) > currentTime {
			finishStatus = consts.LpcWaitStatus
		} else {
			// 获取LU数据
			lu, luExists := lpcLuDataMap[lessonID]
			if luExists {
				// 根据播放类型判断完课状态
				isFinish := int64(0)
				switch lessonInfo.PlayType {
				case dal.PLAY_TYPE_LUBOKE:
					isFinish = lu.IsLbpAttendFinish
				case dal.PLAY_TYPE_AI_HUDONG, dal.PLAY_TYPE_AI_INTERACT:
					isFinish = lu.IsAiFinish
				default:
					isFinish = lu.IsAttendFinish
				}
				if isFinish > 0 {
					finishStatus = consts.LpcSuccessStatus
				}
			}
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, finishStatus)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC完课状态】", "章节开始时间大于当前时间且根据章节类型确定lu中is_lbp_attend_finish，is_ai_finish，is_attend_finish某一个为1")
	return
}

// GetLpcPlayStatus 获取LPC回放状态
// 对应PHP中的playStatus字段
func (s *Format) GetLpcPlayStatus(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLpcLu, []string{"playback_time"}) {
		return
	}

	// 获取课程信息（包含开始时间）
	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonInfoMap := lessonInfoData.(map[int64]dal.LessonInfo)

	// 获取LU数据（包含回放时长）
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLpcStudentData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	lpcLuDataMap := luData.(map[int64]*dataproxy.GetLpcListByCourseStudentResp)

	currentTime := time.Now().Unix()

	for _, lessonID := range s.param.LessonIDs {
		playStatus := consts.LpcUndoneStatus

		// 获取章节信息
		lessonInfo, lessonExists := lessonInfoMap[lessonID]
		if !lessonExists {
			_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playStatus)
			continue
		}

		// 检查章节是否还未开始
		if int64(lessonInfo.StartTime) > currentTime {
			playStatus = consts.LpcWaitStatus
		} else {
			// 获取LU数据
			lu, luExists := lpcLuDataMap[lessonID]
			if luExists {
				// 判断回放时长是否大于等于300秒（5分钟）
				if lu.PlaybackTime >= 300 {
					playStatus = consts.LpcSuccessStatus
				}
			}
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, playStatus)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC回放数据】", "章节开始时间大约现在且lu中playback_time大于300")
	return
}

// GetLpcPreViewData 获取LPC预习数据
// 对应PHP中的preView字段
func (s *Format) GetLpcPreViewData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLpcLu, []string{"exam5", "exam13"}) {
		return
	}

	// 获取LU数据（包含预习相关字段）
	luData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLpcStudentData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	lpcLuDataMap := luData.(map[int64]*dataproxy.GetLpcListByCourseStudentResp)

	// 获取课程信息
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)
	gradeId := int(courseData.MainGradeId)
	exam := "exam5"
	if slices.Contains([]int{2, 3, 4, 20, 5, 6, 7, 30, 50}, gradeId) {
		exam = "exam13"
	}

	for _, lessonID := range s.param.LessonIDs {
		preViewData := "-"

		// 获取LU数据
		lu, luExists := lpcLuDataMap[lessonID]
		if luExists {
			if exam == "exam5" && (lu.Exam5.RightNum > 0 || lu.Exam5.ParticipateNum > 0 || lu.Exam5.TotalNum > 0) {
				// 格式化为"对|答|总"的格式
				preViewData = fmt.Sprintf("%d|%d|%d",
					lu.Exam5.RightNum,
					lu.Exam5.ParticipateNum,
					lu.Exam5.TotalNum)
			}
			if exam == "exam13" && (lu.Exam13.RightNum > 0 || lu.Exam13.ParticipateNum > 0 || lu.Exam13.TotalNum > 0) {
				preViewData = fmt.Sprintf("%d|%d|%d",
					lu.Exam13.RightNum,
					lu.Exam13.ParticipateNum,
					lu.Exam13.TotalNum)
			}
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, preViewData)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC预习数据】", "lu中exam5或者exam13中，right_num，participate_num，total_num为对答总")
	return
}

// GetLpcTangTangExamStatData 获取LPC堂堂测状态数据
// 对应PHP中的tangtangExamStat字段
func (s *Format) GetLpcTangTangExamStatData(ctx *gin.Context) (err error) {
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLpcLu, []string{"exam10"}) {
		return
	}

	// 获取课程信息（包含开始时间）
	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonMap := lessonInfoData.(map[int64]dal.LessonInfo)

	// 获取LPC学生数据（包含exam10字段）
	lpcData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLpcStudentData", []interface{}{s.param.CourseID, s.param.StudentUid})
	if err != nil {
		return
	}
	lpcLuMap := lpcData.(map[int64]*dataproxy.GetLpcListByCourseStudentResp)

	for _, lessonID := range s.param.LessonIDs {
		tangtangExamStat := "-"

		// 获取当前章节信息
		if lessonInfo, ok := lessonMap[lessonID]; ok {
			currentTime := time.Now().Unix()

			// 如果当前时间小于课程开始时间，返回'-'
			if currentTime < int64(lessonInfo.StartTime) {
				tangtangExamStat = "-"
			} else if lpcInfo, exists := lpcLuMap[lessonID]; exists && lpcInfo.Exam10 != nil && lpcInfo.Exam10.IsSubmit == 1 {
				// 如果exam10存在且已提交，返回格式化的字符串：right_num|participate_num|total_num
				tangtangExamStat = fmt.Sprintf("%d|%d|%d",
					lpcInfo.Exam10.RightNum,
					lpcInfo.Exam10.ParticipateNum,
					lpcInfo.Exam10.TotalNum)
			} else {
				tangtangExamStat = "-"
			}
		}

		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, tangtangExamStat)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC堂堂测状态数据】", "开始时间大于当前时间，lu中exam10中is_submit已提交，对答总：lu中exam10中right_num，participate_num，total_num")
	return
}

// GetLpcStrengthPracticeData 获取LPC巩固练习状态数据
// 对应PHP中的strengthPracticeStatus字段
func (s *Format) GetLpcStrengthPracticeData(ctx *gin.Context) (err error) {
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)

	hwBindExamsMap := make(map[int64]bool)
	lessonStrengthPracticeStatusMap := make(map[int64]int)
	if dal.IsLpcByCourse(ctx, courseData) {
		hwBindExams, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkBindExams", []interface{}{s.param.LessonIDs, consts.BindTypeHomework})
		if err != nil {
			return err
		}
		hwBindExamsMap = hwBindExams.(map[int64]bool)

		lessonStrengthPracticeStatus, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonStrengthPracticeStatus", []interface{}{s.param.CourseID, s.param.StudentUid})
		if err != nil {
			return err
		}
		lessonStrengthPracticeStatusMap = lessonStrengthPracticeStatus.(map[int64]int)
	}

	for _, lessonID := range s.param.LessonIDs {
		strengthPracticeStatus := consts.PracticeStatusNoHava
		if hwBindExamsMap[lessonID] {
			if status, exists := lessonStrengthPracticeStatusMap[lessonID]; exists {
				strengthPracticeStatus = status
			}
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, consts.PracticeStatusMap[strengthPracticeStatus])
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC巩固练习状态数据】", "检查章节是否绑定巩固练习，根据年级和LU中exam7字段判断状态")
	return
}

// GetLpcLessonReportData 获取LPC课堂报告数据
// 对应PHP中的getLpcLessonReportData方法
func (s *Format) GetLpcLessonReportData(ctx *gin.Context) (err error) {
	// 获取课程信息（包含开始时间）
	lessonInfoData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonInfoMap", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	lessonInfoMap := lessonInfoData.(map[int64]dal.LessonInfo)

	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)

	lessonReportMap := make(map[int64]string)
	if dal.IsLpcByCourse(ctx, courseData) {
		// 获取课堂报告数据
		lessonReportData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonReportData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
		if err != nil {
			return err
		}
		lessonReportMap = lessonReportData.(map[int64]string)
	}

	currentTime := time.Now().Unix()

	for _, lessonID := range s.param.LessonIDs {
		lessonReportUrl := []string{}
		lessonReportStatus := 0

		if lessonInfo, exists := lessonInfoMap[lessonID]; exists {
			if currentTime >= int64(lessonInfo.StartTime) {
				// 检查是否有课堂报告数据
				if reportUrl, hasReport := lessonReportMap[lessonID]; hasReport {
					// 有报告数据，生成短链接
					lessonReportStatus = 2
					if reportUrl != "" {
						shortUrl, shortErr := moat.NewClient().GetShortUrl(ctx, reportUrl)
						if shortErr != nil {
							zlog.Errorf(ctx, "GetShortUrl failed for lesson %d: %v", lessonID, shortErr)
						} else {
							lessonReportUrl = append(lessonReportUrl, shortUrl)
						}
					}
				} else {
					// 没有报告数据
					lessonReportUrl = []string{}
					lessonReportStatus = 1
				}
			}
		}

		// 输出lessonReportUrl字段
		_ = s.AddOutputStudent(ctx, lessonID, "lessonReportUrl", lessonReportUrl)
		// 输出lessonReportStatus字段
		_ = s.AddOutputStudent(ctx, lessonID, "lessonReportStatus", lessonReportStatus)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【LPC课堂报告数据】", "获取班主任/销售课堂报告，根据章节开始时间和报告数据判断状态，生成短链接")
	return
}

// GetDeerEloquenceHomeworkLevel 获取小鹿口才作业等级
// 对应PHP中的getDeerEloquenceHomeworkLevel方法
func (s *Format) GetDeerEloquenceHomeworkLevel(ctx *gin.Context) (err error) {
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)

	deerDataMap := make(map[int64]*models.DeerDataLUDeerSpecialLPC)
	if dal.IsLpcByCourse(ctx, courseData) {
		// 获取小鹿数据
		deerData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDeerData", []interface{}{s.param.StudentUid, s.param.LessonIDs})
		if err != nil {
			return err
		}
		deerDataMap = deerData.(map[int64]*models.DeerDataLUDeerSpecialLPC)
	}

	for _, lessonID := range s.param.LessonIDs {
		var deerEloquenceHomeworkLevel string

		// 获取当前章节的小鹿数据
		if deerInfo, exists := deerDataMap[lessonID]; exists && deerInfo != nil {
			// 使用小鹿口才作业等级
			deerEloquenceHomeworkLevel = consts.GetDeerHomeworkLevel(deerInfo.DeerEloquenceHomeworkLevel)
		} else {
			// 没有数据时返回默认值
			deerEloquenceHomeworkLevel = "-"
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, deerEloquenceHomeworkLevel)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【小鹿口才作业等级】", "从小鹿数据表获取口才作业等级，1→S, 2→A, 3→B，无数据时返回'-'")
	return
}

// GetDeerProgrammingHomeworkLevel 获取小鹿编程作业等级
// 对应PHP中的getDeerProgrammingHomeworkLevel方法
func (s *Format) GetDeerProgrammingHomeworkLevel(ctx *gin.Context) (err error) {
	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)

	deerDataMap := make(map[int64]*models.DeerDataLUDeerSpecialLPC)
	if dal.IsLpcByCourse(ctx, courseData) {
		// 获取小鹿数据
		deerData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetDeerData", []interface{}{s.param.StudentUid, s.param.LessonIDs})
		if err != nil {
			return err
		}
		deerDataMap = deerData.(map[int64]*models.DeerDataLUDeerSpecialLPC)
	}

	for _, lessonID := range s.param.LessonIDs {
		deerProgrammingHomeworkLevel := "-"

		// 获取当前章节的小鹿数据
		if deerInfo, exists := deerDataMap[lessonID]; exists && deerInfo != nil {
			// 使用小鹿编程作业等级
			deerProgrammingHomeworkLevel = consts.GetDeerHomeworkLevel(deerInfo.DeerProgrammingHomeworkLevel)
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, deerProgrammingHomeworkLevel)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【小鹿编程作业等级】", "从小鹿数据表获取编程作业等级，1→S, 2→A, 3→B，无数据时返回'-'")
	return
}

// GetDeerLessonReport 获取小鹿章节报告
// 对应PHP中的getDeerLessonReport方法
func (s *Format) GetDeerLessonReport(ctx *gin.Context) (err error) {
	// 获取LU Common数据 - is_submit_lesson_work字段
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"is_submit_lesson_work"}) {
		return
	}

	// 获取LU Common数据
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	luCommonMap := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	reportUrlData, err := writereport.NewClient().GetReportUrl(ctx)
	if err != nil {
		return
	}

	// 为每个课节处理数据
	for _, lessonID := range s.param.LessonIDs {
		// 获取is_submit_lesson_work状态
		isSubmitLessonWork := int64(0)
		if luData, exists := luCommonMap[lessonID]; exists {
			isSubmitLessonWork = luData.IsSubmitLessonWork
		}

		// 生成报告URL
		deerLessonReportUrl := ""
		if isSubmitLessonWork > 0 {
			// 替换URL模板中的占位符
			studentReportUrl := strings.ReplaceAll(reportUrlData.LessonReportUrl, "__lessonId__", cast.ToString(lessonID))
			studentReportUrl = strings.ReplaceAll(studentReportUrl, "__studentUid__", cast.ToString(s.param.StudentUid))

			// 生成短链接
			shortUrl, err := moat.NewClient().GetShortUrl(ctx, studentReportUrl)
			if err != nil {
				zlog.Errorf(ctx, "GetShortUrl failed for lesson %d: %v", lessonID, err)
			} else {
				deerLessonReportUrl = shortUrl
			}
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, "isSubmitLessonWork", isSubmitLessonWork)
		_ = s.AddOutputStudent(ctx, lessonID, "deerLessonReportUrl", deerLessonReportUrl)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【小鹿章节报告数据】", "获取LU数据中的is_submit_lesson_work字段，调用writereport API获取报告URL模板，根据提交状态生成短链接")
	return
}

// GetLessonHomeWork 获取章节作品提交状态
// 对应PHP中的getLessonHomeWork方法
func (s *Format) GetLessonHomeWork(ctx *gin.Context) (err error) {
	// 获取LU Common数据 - is_submit_lesson_work字段
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"is_submit_lesson_work"}) {
		return
	}

	// 获取LU Common数据
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	luCommonMap := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	// 为每个课节处理数据
	for _, lessonID := range s.param.LessonIDs {
		// 获取is_submit_lesson_work状态
		isSubmitLessonWork := int64(0)
		if luData, exists := luCommonMap[lessonID]; exists {
			isSubmitLessonWork = luData.IsSubmitLessonWork
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, isSubmitLessonWork)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【章节作品提交状态】", "获取LU数据中的is_submit_lesson_work字段，返回章节作品是否已提交的状态")
	return
}

// GetZhiboLessonReport 获取直播课报告
// 对应PHP中的getZhiboLessonReport方法
func (s *Format) GetZhiboLessonReport(ctx *gin.Context) (err error) {
	// 获取LU Common数据 - is_generate_lesson_report字段
	if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceCommonLu, []string{"is_generate_lesson_report"}) {
		return
	}

	// 获取LU Common数据
	luCommonData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCommonLuData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
	if err != nil {
		return
	}
	luCommonMap := luCommonData.(map[int64]*dataproxy.GetCommonLuResp)

	courseInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetCourseInfo", []interface{}{s.param.CourseID})
	if err != nil {
		return
	}
	courseData := courseInfo.(dal.CourseInfo)

	lessonReportMap := make(map[int64]string)
	if dal.IsLpcByCourse(ctx, courseData) {
		// 获取课堂报告数据
		lessonReportData, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonReportData", []interface{}{s.param.LessonIDs, s.param.StudentUid})
		if err != nil {
			return err
		}
		lessonReportMap = lessonReportData.(map[int64]string)
	}

	// 为每个课节处理数据
	for _, lessonID := range s.param.LessonIDs {
		// 获取is_generate_lesson_report状态
		isGenerateLessonReport := int64(0)
		if luData, exists := luCommonMap[lessonID]; exists {
			isGenerateLessonReport = luData.IsGenerateLessonReport
		}

		// 获取课堂报告URL
		zhiboLessonReport := ""
		if isGenerateLessonReport > 0 {
			if reportUrl, hasReport := lessonReportMap[lessonID]; hasReport {
				// 生成短链接
				shortUrl, err := moat.NewClient().GetShortUrl(ctx, reportUrl)
				if err != nil {
					zlog.Errorf(ctx, "GetShortUrl failed for lesson %d: %v", lessonID, err)
				} else {
					zhiboLessonReport = shortUrl
				}
			}
		}

		// 输出结果
		_ = s.AddOutputStudent(ctx, lessonID, "isGenerateLessonReport", isGenerateLessonReport)
		_ = s.AddOutputStudent(ctx, lessonID, s.rule.Key, zhiboLessonReport)
	}

	_ = s.AddDataSource(ctx, s.rule.Key, "【直播课报告数据】", "获取LU数据中的is_generate_lesson_report字段和课堂报告URL，根据生成状态返回短链接")
	return
}
