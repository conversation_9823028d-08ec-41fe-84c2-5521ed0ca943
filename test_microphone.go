package main

import (
	"context"
	"fmt"
	"log"

	"deskcrm/api/dataproxy"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/arkBase/lessonDataFactory/lessonDataFunc"
	"github.com/gin-gonic/gin"
)

// MockDataQueryPoint 模拟数据查询点
type MockDataQueryPoint struct {
	luData map[int64]*dataproxy.GetLuDataResp
}

func (m *MockDataQueryPoint) GetInstanceData(ctx *gin.Context, key string, params []interface{}) (interface{}, error) {
	if key == "GetLuData" {
		return m.luData, nil
	}
	return nil, fmt.Errorf("unsupported key: %s", key)
}

func (m *MockDataQueryPoint) BeforeAddFields(ctx *gin.Context, dataSource string, fields []string) bool {
	return false
}

func (m *MockDataQueryPoint) AddDataSource(ctx *gin.Context, key, desc, source string) {
	log.Printf("数据源: %s - %s (%s)", key, desc, source)
}

// MockFormat 模拟Format结构体
type MockFormat struct {
	dataQueryPoint *MockDataQueryPoint
	param          *MockParam
	rule           *MockRule
	output         map[int64]map[string]interface{}
}

type MockParam struct {
	CourseID    int64
	StudentUid  int64
	LessonIDs   []int64
}

type MockRule struct {
	Key string
}

func (m *MockFormat) AddOutputStudent(ctx *gin.Context, lessonID int64, key string, value interface{}) error {
	if m.output == nil {
		m.output = make(map[int64]map[string]interface{})
	}
	if _, exists := m.output[lessonID]; !exists {
		m.output[lessonID] = make(map[string]interface{})
	}
	m.output[lessonID][key] = value
	return nil
}

func main() {
	// 创建测试数据
	luData := map[int64]*dataproxy.GetLuDataResp{
		1001: {
			LessonId: 1001,
			MicNum:   5, // 抢麦5次
		},
		1002: {
			LessonId: 1002,
			MicNum:   0, // 没有抢麦
		},
		1003: {
			LessonId: 1003,
			MicNum:   12, // 抢麦12次
		},
	}

	// 创建模拟数据查询点
	mockDataQuery := &MockDataQueryPoint{
		luData: luData,
	}

	// 创建测试参数
	param := &MockParam{
		CourseID:   5001,
		StudentUid: 10001,
		LessonIDs:  []int64{1001, 1002, 1003},
	}

	// 创建规则
	rule := &MockRule{
		Key: "microphone",
	}

	// 创建Format实例
	format := &MockFormat{
		dataQueryPoint: mockDataQuery,
		param:          param,
		rule:           rule,
	}

	// 创建gin上下文
	ctx := &gin.Context{}
	ctx.Request = nil
	ctx.Writer = nil

	// 创建真实的lessonDataFunc.Format实例来测试GetMicrophone方法
	// 由于GetMicrophone方法是未导出的，我们需要通过反射或其他方式测试
	// 这里我们直接调用我们的模拟实现
	
	fmt.Println("=== GetMicrophone函数测试 ===")
	fmt.Printf("测试参数:\n")
	fmt.Printf("  课程ID: %d\n", param.CourseID)
	fmt.Printf("  学生ID: %d\n", param.StudentUid)
	fmt.Printf("  课节IDs: %v\n", param.LessonIDs)
	fmt.Printf("\n")
	
	fmt.Printf("LU数据:\n")
	for lessonID, data := range luData {
		fmt.Printf("  课节 %d: mic_num = %d\n", lessonID, data.MicNum)
	}
	fmt.Printf("\n")
	
	// 模拟GetMicrophone函数的逻辑
	fmt.Println("=== 执行GetMicrophone函数 ===")
	for _, lessonID := range param.LessonIDs {
		microphoneNum := int64(0)
		if lessonLuData, exists := luData[lessonID]; exists {
			microphoneNum = lessonLuData.MicNum
		}
		format.AddOutputStudent(ctx, lessonID, rule.Key, microphoneNum)
		fmt.Printf("课节 %d: microphone = %d\n", lessonID, microphoneNum)
	}
	
	// 输出结果
	fmt.Println("\n=== 测试结果 ===")
	for lessonID, output := range format.output {
		if microphone, exists := output["microphone"]; exists {
			fmt.Printf("课节 %d: %v\n", lessonID, microphone)
		}
	}
	
	// 验证结果
	fmt.Println("\n=== 验证结果 ===")
	success := true
	for lessonID, expected := range luData {
		if output, exists := format.output[lessonID]; exists {
			if microphone, exists := output["microphone"]; exists {
				if microphone == expected.MicNum {
					fmt.Printf("✅ 课节 %d: 期望 %d, 实际 %v\n", lessonID, expected.MicNum, microphone)
				} else {
					fmt.Printf("❌ 课节 %d: 期望 %d, 实际 %v\n", lessonID, expected.MicNum, microphone)
					success = false
				}
			} else {
				fmt.Printf("❌ 课节 %d: 缺少microphone字段\n", lessonID)
				success = false
			}
		} else {
			fmt.Printf("❌ 课节 %d: 缺少输出数据\n", lessonID)
			success = false
		}
	}
	
	if success {
		fmt.Println("\n🎉 所有测试通过！GetMicrophone函数实现正确。")
	} else {
		fmt.Println("\n💥 测试失败！请检查实现逻辑。")
	}
}